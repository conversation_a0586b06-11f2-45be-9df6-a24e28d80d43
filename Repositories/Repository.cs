using HeliosETL.Services;
using Serilog;

namespace HeliosETL.Repositories;

public class Repository 
{
    protected Database _db;
    private readonly ILogger _logger;
    
    /// <summary>
    /// Entity name to table name mapping
    /// </summary>
    public Dictionary<string, string> TableName { get; set; } = new Dictionary<string, string>();

    public Repository()
    {
        _db = new Database();
        _logger = LoggingService.Instance.GetLogger<Repository>();
        Init();
    }
    
    private void InitTableName()
    {
        TableName.Add("AbstractActivite", "hls_activite");
        TableName.Add("AbstractIssue", "hls_issue");
        TableName.Add("Commanditaire", "hls_commanditaire");
        TableName.Add("DocumentContractuel", "hls_document_contractuel");
        TableName.Add("DomaineMetier", "hls_domaine_metier");
        TableName.Add("IssueOrigine", "hls_issue_origine");
        TableName.Add("IssuePieceJointe", "hls_issue_piece_jointe");
        TableName.Add("IssuePriorite", "hls_issue_priorite");
        TableName.Add("IssueRelation", "hls_issue_relation");
        TableName.Add("IssueStatut", "hls_issue_statut");
        TableName.Add("Journal", "hls_journal");
        TableName.Add("JournalDetails", "hls_journal_details");
        TableName.Add("NiveauComplexite", "hls_niveau_complexite");
        TableName.Add("Personne", "hls_personne");
        TableName.Add("TypeDocument", "hls_document_type");
        TableName.Add("TypeMission", "hls_mission_type");
        TableName.Add("TypePersonne", "hls_personne_type");
        TableName.Add("TypeProjet", "hls_projet_type");
    }
    
    protected string GetTableName(string entityName)
    {
        return TableName.TryGetValue(entityName, out var tableName) ? tableName : entityName;
    }

    private void Init()
    {
        if (!_db.SetupDatabase())
        {
            _logger.Error("Failed to setup database connections");
            return;
        }
        _logger.Debug("Initializing Repository service");
        _logger.Information("Repository service initialized successfully");
    }
}