using HeliosETL.Const;
using HeliosETL.Models;
using Microsoft.Data.Sqlite;
using HeliosETL.Exceptions;

namespace HeliosETL.Repositories
{
    public class ETLHistoryRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(ETLHistory))
            {
                throw new ArgumentException(Error.InvalidType, typeof(ETLHistory).Name);
            }
            HashSet<ETLHistory> etlHistories = new HashSet<ETLHistory>();

            if (this._db.Sqlite == null)
            {
                throw new DatabaseException("SQLite connection is not initialized");
            }

            try
            {
                this._db.Sqlite.Open();
                using (var command = new SqliteCommand("SELECT * FROM ETLHistory", this._db.Sqlite))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ETLHistory etlHistory = new ETLHistory()
                            {
                                oldId = Convert.ToInt64(reader["oldId"]),
                                newId = Convert.ToInt64(reader["newId"]),
                                tableName = reader["tableName"].ToString() ?? string.Empty,
                                action = reader["action"].ToString() ?? string.Empty,
                                errorMessage = reader["errorMessage"].ToString() ?? string.Empty,
                                state = (ETLState)Enum.Parse(typeof(ETLState), reader["state"].ToString() ?? "Failed"),
                                date = Convert.ToDateTime(reader["date"])
                            };

                            etlHistories.Add(etlHistory);
                        }
                    }
                }

                return etlHistories.Cast<T>().ToHashSet();
            }
            catch (Exception ex)
            {
                this._db.Sqlite.Close();
                throw;
            }
            finally
            {
                this._db.Sqlite.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(ETLHistory))
            {
                throw new ArgumentException(Error.InvalidType, typeof(ETLHistory).Name);
            }

            if (this._db.Sqlite == null)
            {
                throw new DatabaseException("SQLite connection is not initialized");
            }

            try
            {
                this._db.Sqlite.Open();
                using (var command = new SqliteCommand("SELECT * FROM ETLHistory WHERE oldId = @id", this._db.Sqlite))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            ETLHistory etlHistory = new ETLHistory()
                            {
                                oldId = Convert.ToInt64(reader["oldId"]),
                                newId = Convert.ToInt64(reader["newId"]),
                                tableName = reader["tableName"].ToString() ?? string.Empty,
                                action = reader["action"].ToString() ?? string.Empty,
                                errorMessage = reader["errorMessage"].ToString() ?? string.Empty,
                                state = (ETLState)Enum.Parse(typeof(ETLState), reader["state"].ToString() ?? "Failed"),
                                date = Convert.ToDateTime(reader["date"])
                            };
                            return (T)(object)etlHistory;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                this._db.Sqlite.Close();
                throw;
            }
            finally
            {
                this._db.Sqlite.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(ETLHistory))
            {
                throw new ArgumentException(Error.InvalidType, typeof(ETLHistory).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var etlHistory = entity as ETLHistory;
            if (etlHistory == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.Sqlite == null)
            {
                throw new DatabaseException("SQLite connection is not initialized");
            }

            try
            {
                this._db.Sqlite.Open();
                string sql = @"INSERT INTO ETLHistory (oldId, newId, tableName, action, errorMessage, state, date) VALUES (@oldId, @newId, @tableName, @action, @errorMessage, @state, @date)";
                using (var command = new SqliteCommand(sql, this._db.Sqlite))
                {
                    command.Parameters.AddWithValue("@oldId", etlHistory.oldId);
                    command.Parameters.AddWithValue("@newId", etlHistory.newId);
                    command.Parameters.AddWithValue("@tableName", etlHistory.tableName ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@action", etlHistory.action ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@errorMessage", etlHistory.errorMessage ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@state", etlHistory.state.ToString());
                    command.Parameters.AddWithValue("@date", etlHistory.date);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.Sqlite.Close();
                throw;
            }
            finally
            {
                this._db.Sqlite.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(ETLHistory))
            {
                throw new ArgumentException(Error.InvalidType, typeof(ETLHistory).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var etlHistory = entity as ETLHistory;
            if (etlHistory == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.Sqlite == null)
            {
                throw new DatabaseException("SQLite connection is not initialized");
            }

            try
            {
                this._db.Sqlite.Open();
                string sql = @"UPDATE ETLHistory SET newId=@newId, tableName=@tableName, action=@action, errorMessage=@errorMessage, state=@state, date=@date WHERE oldId=@oldId";
                using (var command = new SqliteCommand(sql, this._db.Sqlite))
                {
                    command.Parameters.AddWithValue("@oldId", etlHistory.oldId);
                    command.Parameters.AddWithValue("@newId", etlHistory.newId);
                    command.Parameters.AddWithValue("@tableName", etlHistory.tableName ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@action", etlHistory.action ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@errorMessage", etlHistory.errorMessage ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@state", etlHistory.state.ToString());
                    command.Parameters.AddWithValue("@date", etlHistory.date);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.Sqlite.Close();
                throw;
            }
            finally
            {
                this._db.Sqlite.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(ETLHistory))
            {
                throw new ArgumentException(Error.InvalidType, typeof(ETLHistory).Name);
            }
            var etlHistory = entity as ETLHistory;

            if (this._db.Sqlite == null)
            {
                throw new DatabaseException("SQLite connection is not initialized");
            }

            try
            {
                this._db.Sqlite.Open();
                using (var command = new SqliteCommand("DELETE FROM ETLHistory WHERE oldId = @id", this._db.Sqlite))
                {
                    command.Parameters.AddWithValue("@id", etlHistory.oldId);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.Sqlite.Close();
                throw;
            }
            finally
            {
                this._db.Sqlite.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(ETLHistory))
            {
                throw new ArgumentException(Error.InvalidType, typeof(ETLHistory).Name);
            }

            if (this._db.Sqlite == null)
            {
                throw new DatabaseException("SQLite connection is not initialized");
            }

            try
            {
                this._db.Sqlite.Open();
                using (var command = new SqliteCommand("DELETE FROM ETLHistory WHERE oldId = @id", this._db.Sqlite))
                {
                    command.Parameters.AddWithValue("@id", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.Sqlite.Close();
                throw;
            }
            finally
            {
                this._db.Sqlite.Close();
            }
        }
    }
}