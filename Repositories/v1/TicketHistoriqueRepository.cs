using HeliosETL.Const;
using HeliosETL.Exceptions;
using HeliosETL.Models.v1;
using Microsoft.Data.SqlClient;

namespace HeliosETL.Repositories.v1
{
    public class TicketHistoriqueRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(TicketsHistorique))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TicketsHistorique).Name);
            }
            HashSet<TicketsHistorique> ticketsHistorique = new HashSet<TicketsHistorique>();

            if (this._db.SqlServer == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.SqlServer.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.SqlServer.Open();

                using (var command = new SqlCommand("SELECT * FROM TicketsHistorique", this._db.SqlServer))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            TicketsHistorique historique = new TicketsHistorique()
                            {
                                IdHistorique = Convert.ToInt32(reader["idHistorique"]),
                                IdTickets = Convert.ToInt32(reader["idTickets"]),
                                DateModificationn = Convert.ToDateTime(reader["datModificationn"]),
                                Correspondant = reader["correspondant"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                NoteInterne = Convert.ToInt32(reader["noteInterne"]),
                                PieceJointe = Convert.ToInt32(reader["pieceJointe"]),
                                EnvoiEmail = Convert.ToInt32(reader["envoiEmail"]),
                                NoteType = Convert.ToInt32(reader["noteType"]),
                                Temps = Convert.ToInt32(reader["temps"])
                            };

                            ticketsHistorique.Add(historique);
                        }
                    }
                }

                return ticketsHistorique.Cast<T>().ToHashSet();
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.SqlServer.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(TicketsHistorique))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TicketsHistorique).Name);
            }

            if (this._db.SqlServer == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.SqlServer.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.SqlServer.Open();

                using (var command = new SqlCommand("SELECT * FROM TicketsHistorique WHERE idHistorique = @id", this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            TicketsHistorique historique = new TicketsHistorique()
                            {
                                IdHistorique = Convert.ToInt32(reader["idHistorique"]),
                                IdTickets = Convert.ToInt32(reader["idTickets"]),
                                DateModificationn = Convert.ToDateTime(reader["datModificationn"]),
                                Correspondant = reader["correspondant"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                NoteInterne = Convert.ToInt32(reader["noteInterne"]),
                                PieceJointe = Convert.ToInt32(reader["pieceJointe"]),
                                EnvoiEmail = Convert.ToInt32(reader["envoiEmail"]),
                                NoteType = Convert.ToInt32(reader["noteType"]),
                                Temps = Convert.ToInt32(reader["temps"])
                            };
                            return (T)(object)historique;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.SqlServer.Close();
            }
        }

        public HashSet<T> GetByTicketId<T>(int ticketId)
        {
            if (typeof(T) != typeof(TicketsHistorique))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TicketsHistorique).Name);
            }
            HashSet<TicketsHistorique> ticketsHistorique = new HashSet<TicketsHistorique>();

            if (this._db.SqlServer == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.SqlServer.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.SqlServer.Open();

                using (var command = new SqlCommand("SELECT * FROM TicketsHistorique WHERE idTickets = @ticketId", this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@ticketId", ticketId);
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            TicketsHistorique historique = new TicketsHistorique()
                            {
                                IdHistorique = Convert.ToInt32(reader["idHistorique"]),
                                IdTickets = Convert.ToInt32(reader["idTickets"]),
                                DateModificationn = Convert.ToDateTime(reader["datModificationn"]),
                                Correspondant = reader["correspondant"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                NoteInterne = Convert.ToInt32(reader["noteInterne"]),
                                PieceJointe = Convert.ToInt32(reader["pieceJointe"]),
                                EnvoiEmail = Convert.ToInt32(reader["envoiEmail"]),
                                NoteType = Convert.ToInt32(reader["noteType"]),
                                Temps = Convert.ToInt32(reader["temps"])
                            };

                            ticketsHistorique.Add(historique);
                        }
                    }
                }

                return ticketsHistorique.Cast<T>().ToHashSet();
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.SqlServer.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(TicketsHistorique))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TicketsHistorique).Name);
            }

            var historique = entity as TicketsHistorique;
            if (historique == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.SqlServer == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.SqlServer.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.SqlServer.Open();

                string sql = @"INSERT INTO TicketsHistorique (idTickets, datModificationn, correspondant, description, noteInterne, pieceJointe, envoiEmail, noteType, temps) 
                              VALUES (@idTickets, @datModificationn, @correspondant, @description, @noteInterne, @pieceJointe, @envoiEmail, @noteType, @temps)";
                using (var command = new SqlCommand(sql, this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@idTickets", historique.IdTickets);
                    command.Parameters.AddWithValue("@datModificationn", historique.DateModificationn);
                    command.Parameters.AddWithValue("@correspondant", historique.Correspondant ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", historique.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@noteInterne", historique.NoteInterne);
                    command.Parameters.AddWithValue("@pieceJointe", historique.PieceJointe);
                    command.Parameters.AddWithValue("@envoiEmail", historique.EnvoiEmail);
                    command.Parameters.AddWithValue("@noteType", historique.NoteType);
                    command.Parameters.AddWithValue("@temps", historique.Temps);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.SqlServer.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(TicketsHistorique))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TicketsHistorique).Name);
            }

            var historique = entity as TicketsHistorique;
            if (historique == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.SqlServer == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.SqlServer.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.SqlServer.Open();

                string sql = @"UPDATE TicketsHistorique 
                              SET idTickets=@idTickets, 
                                  datModificationn=@datModificationn, 
                                  correspondant=@correspondant, 
                                  description=@description, 
                                  noteInterne=@noteInterne, 
                                  pieceJointe=@pieceJointe, 
                                  envoiEmail=@envoiEmail, 
                                  noteType=@noteType, 
                                  temps=@temps 
                              WHERE idHistorique=@idHistorique";
                using (var command = new SqlCommand(sql, this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@idTickets", historique.IdTickets);
                    command.Parameters.AddWithValue("@datModificationn", historique.DateModificationn);
                    command.Parameters.AddWithValue("@correspondant", historique.Correspondant ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", historique.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@noteInterne", historique.NoteInterne);
                    command.Parameters.AddWithValue("@pieceJointe", historique.PieceJointe);
                    command.Parameters.AddWithValue("@envoiEmail", historique.EnvoiEmail);
                    command.Parameters.AddWithValue("@noteType", historique.NoteType);
                    command.Parameters.AddWithValue("@temps", historique.Temps);
                    command.Parameters.AddWithValue("@idHistorique", historique.IdHistorique);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.SqlServer.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(TicketsHistorique))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TicketsHistorique).Name);
            }
            var historique = entity as TicketsHistorique;
            if (historique == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.SqlServer == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.SqlServer.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.SqlServer.Open();

                using (var command = new SqlCommand("DELETE FROM TicketsHistorique WHERE idHistorique = @id", this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@id", historique.IdHistorique);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.SqlServer.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(TicketsHistorique))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TicketsHistorique).Name);
            }

            if (this._db.SqlServer == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.SqlServer.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.SqlServer.Open();

                using (var command = new SqlCommand("DELETE FROM TicketsHistorique WHERE idHistorique = @id", this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@id", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.SqlServer.Close();
            }
        }
    }
}