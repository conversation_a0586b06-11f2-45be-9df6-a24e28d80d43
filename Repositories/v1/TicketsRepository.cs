using HeliosETL.Const;
using HeliosETL.Exceptions;
using HeliosETL.Models.v1;
using Microsoft.Data.SqlClient;

namespace HeliosETL.Repositories.v1
{
    public class TicketsRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(Tickets))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Tickets).Name);
            }
            HashSet<Tickets> tickets = new HashSet<Tickets>();

            if (this._db.SqlServer == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.SqlServer.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.SqlServer.Open();

                using (var command = new SqlCommand("SELECT * FROM Tickets", this._db.SqlServer))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Tickets ticket = new Tickets()
                            {
                                Avertissement = Convert.ToInt32(reader["avertissement"]),
                                Client = reader["client"].ToString() ?? string.Empty,
                                CtNum = reader["ct_num"].ToString() ?? string.Empty,
                                DateCreation = Convert.ToDateTime(reader["dateCreation"]),
                                Demandeur = reader["demandeur"].ToString() ?? string.Empty,
                                Assigne = reader["assigne"].ToString() ?? string.Empty,
                                DernierCorrespondant = reader["dernierCorrespondant"].ToString() ?? string.Empty,
                                IdTickets = Convert.ToInt32(reader["idTickets"]),
                                Niveau = Convert.ToInt32(reader["niveau"]),
                                Priorite = reader["priorite"].ToString() ?? string.Empty,
                                Status = reader["status"].ToString() ?? string.Empty,
                                TempsTotal = Convert.ToInt32(reader["tempsTotal"]),
                                Titre = reader["titre"].ToString() ?? string.Empty,
                                Type = reader["type"].ToString() ?? string.Empty,
                                DateDerniereReponse = Convert.ToDateTime(reader["dateDerniereReponse"]),
                                DatePremiereReponse = Convert.ToDateTime(reader["datePremiereReponse"]),
                                DateRappel = Convert.ToDateTime(reader["dateRappel"]),
                                DateResolution = Convert.ToDateTime(reader["dateResolution"]),
                                Description = reader["description"].ToString() ?? string.Empty,
                                NotificationEnabled = Convert.ToBoolean(reader["notificationEnabled"]),
                                StatusTemps = Convert.ToInt32(reader["statusTemps"]),
                                Categorie = reader["categorie"].ToString() ?? string.Empty,
                                Categorie2 = reader["categorie2"].ToString() ?? string.Empty,
                                Categorie3 = reader["categorie3"].ToString() ?? string.Empty,
                                Pole = reader["pole"].ToString() ?? string.Empty
                            };

                            tickets.Add(ticket);
                        }
                    }
                }

                return tickets.Cast<T>().ToHashSet();
            }
            catch (Exception ex)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.SqlServer.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(Tickets))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Tickets).Name);
            }

            if (this._db.SqlServer == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.SqlServer.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.SqlServer.Open();

                using (var command = new SqlCommand("SELECT * FROM Tickets WHERE idTickets = @id", this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            Tickets ticket = new Tickets()
                            {
                                Avertissement = Convert.ToInt32(reader["avertissement"]),
                                Client = reader["client"].ToString() ?? string.Empty,
                                CtNum = reader["ct_num"].ToString() ?? string.Empty,
                                DateCreation = Convert.ToDateTime(reader["dateCreation"]),
                                Demandeur = reader["demandeur"].ToString() ?? string.Empty,
                                Assigne = reader["assigne"].ToString() ?? string.Empty,
                                DernierCorrespondant = reader["dernierCorrespondant"].ToString() ?? string.Empty,
                                IdTickets = Convert.ToInt32(reader["idTickets"]),
                                Niveau = Convert.ToInt32(reader["niveau"]),
                                Priorite = reader["priorite"].ToString() ?? string.Empty,
                                Status = reader["status"].ToString() ?? string.Empty,
                                TempsTotal = Convert.ToInt32(reader["tempsTotal"]),
                                Titre = reader["titre"].ToString() ?? string.Empty,
                                Type = reader["type"].ToString() ?? string.Empty,
                                DateDerniereReponse = Convert.ToDateTime(reader["dateDerniereReponse"]),
                                DatePremiereReponse = Convert.ToDateTime(reader["datePremiereReponse"]),
                                DateRappel = Convert.ToDateTime(reader["dateRappel"]),
                                DateResolution = Convert.ToDateTime(reader["dateResolution"]),
                                Description = reader["description"].ToString() ?? string.Empty,
                                NotificationEnabled = Convert.ToBoolean(reader["notificationEnabled"]),
                                StatusTemps = Convert.ToInt32(reader["statusTemps"]),
                                Categorie = reader["categorie"].ToString() ?? string.Empty,
                                Categorie2 = reader["categorie2"].ToString() ?? string.Empty,
                                Categorie3 = reader["categorie3"].ToString() ?? string.Empty,
                                Pole = reader["pole"].ToString() ?? string.Empty
                            };
                            return (T)(object)ticket;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.SqlServer.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(Tickets))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Tickets).Name);
            }
            
            var ticket = entity as Tickets;
            if (ticket == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.SqlServer == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.SqlServer.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.SqlServer.Open();

                string sql = @"INSERT INTO Tickets (avertissement, client, ct_num, dateCreation, demandeur, assigne, dernierCorrespondant, niveau, priorite, status, tempsTotal, titre, type, dateDerniereReponse, datePremiereReponse, dateRappel, dateResolution, description, notificationEnabled, statusTemps, categorie, categorie2, categorie3, pole) VALUES (@avertissement, @client, @ct_num, @dateCreation, @demandeur, @assigne, @dernierCorrespondant, @niveau, @priorite, @status, @tempsTotal, @titre, @type, @dateDerniereReponse, @datePremiereReponse, @dateRappel, @dateResolution, @description, @notificationEnabled, @statusTemps, @categorie, @categorie2, @categorie3, @pole)";
                using (var command = new SqlCommand(sql, this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@avertissement", ticket.Avertissement);
                    command.Parameters.AddWithValue("@client", ticket.Client ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ct_num", ticket.CtNum ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@dateCreation", ticket.DateCreation);
                    command.Parameters.AddWithValue("@demandeur", ticket.Demandeur ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@assigne", ticket.Assigne ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@dernierCorrespondant", ticket.DernierCorrespondant ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@niveau", ticket.Niveau);
                    command.Parameters.AddWithValue("@priorite", ticket.Priorite ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@status", ticket.Status ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@tempsTotal", ticket.TempsTotal);
                    command.Parameters.AddWithValue("@titre", ticket.Titre ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@type", ticket.Type ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@dateDerniereReponse", ticket.DateDerniereReponse);
                    command.Parameters.AddWithValue("@datePremiereReponse", ticket.DatePremiereReponse);
                    command.Parameters.AddWithValue("@dateRappel", ticket.DateRappel);
                    command.Parameters.AddWithValue("@dateResolution", ticket.DateResolution);
                    command.Parameters.AddWithValue("@description", ticket.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@notificationEnabled", ticket.NotificationEnabled);
                    command.Parameters.AddWithValue("@statusTemps", ticket.StatusTemps);
                    command.Parameters.AddWithValue("@categorie", ticket.Categorie ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@categorie2", ticket.Categorie2 ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@categorie3", ticket.Categorie3 ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@pole", ticket.Pole ?? (object)DBNull.Value);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.SqlServer.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(Tickets))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Tickets).Name);
            }
            
            var ticket = entity as Tickets;
            if (ticket == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.SqlServer == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.SqlServer.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.SqlServer.Open();

                string sql = @"UPDATE Tickets SET avertissement=@avertissement, client=@client, ct_num=@ct_num, dateCreation=@dateCreation, demandeur=@demandeur, assigne=@assigne, dernierCorrespondant=@dernierCorrespondant, niveau=@niveau, priorite=@priorite, status=@status, tempsTotal=@tempsTotal, titre=@titre, type=@type, dateDerniereReponse=@dateDerniereReponse, datePremiereReponse=@datePremiereReponse, dateRappel=@dateRappel, dateResolution=@dateResolution, description=@description, notificationEnabled=@notificationEnabled, statusTemps=@statusTemps, categorie=@categorie, categorie2=@categorie2, categorie3=@categorie3, pole=@pole WHERE idTickets=@idTickets";
                using (var command = new SqlCommand(sql, this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@avertissement", ticket.Avertissement);
                    command.Parameters.AddWithValue("@client", ticket.Client ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ct_num", ticket.CtNum ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@dateCreation", ticket.DateCreation);
                    command.Parameters.AddWithValue("@demandeur", ticket.Demandeur ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@assigne", ticket.Assigne ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@dernierCorrespondant", ticket.DernierCorrespondant ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@niveau", ticket.Niveau);
                    command.Parameters.AddWithValue("@priorite", ticket.Priorite ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@status", ticket.Status ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@tempsTotal", ticket.TempsTotal);
                    command.Parameters.AddWithValue("@titre", ticket.Titre ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@type", ticket.Type ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@dateDerniereReponse", ticket.DateDerniereReponse);
                    command.Parameters.AddWithValue("@datePremiereReponse", ticket.DatePremiereReponse);
                    command.Parameters.AddWithValue("@dateRappel", ticket.DateRappel);
                    command.Parameters.AddWithValue("@dateResolution", ticket.DateResolution);
                    command.Parameters.AddWithValue("@description", ticket.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@notificationEnabled", ticket.NotificationEnabled);
                    command.Parameters.AddWithValue("@statusTemps", ticket.StatusTemps);
                    command.Parameters.AddWithValue("@categorie", ticket.Categorie ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@categorie2", ticket.Categorie2 ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@categorie3", ticket.Categorie3 ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@pole", ticket.Pole ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@idTickets", ticket.IdTickets);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.SqlServer.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(Tickets))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Tickets).Name);
            }
            var ticket = entity as Tickets;
            if (ticket == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.SqlServer == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.SqlServer.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.SqlServer.Open();

                using (var command = new SqlCommand("DELETE FROM Tickets WHERE idTickets = @id", this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@id", ticket.IdTickets);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.SqlServer.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(Tickets))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Tickets).Name);
            }

            if (this._db.SqlServer == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.SqlServer.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.SqlServer.Open();

                using (var command = new SqlCommand("DELETE FROM Tickets WHERE idTickets = @id", this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@id", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.SqlServer.Close();
            }
        }
    }
}