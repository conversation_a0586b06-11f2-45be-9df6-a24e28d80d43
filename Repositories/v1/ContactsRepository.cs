using HeliosETL.Const;
using HeliosETL.Exceptions;
using HeliosETL.Models.v1;
using Microsoft.Data.SqlClient;

namespace HeliosETL.Repositories.v1
{
    public class ContactsRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(Contacts))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Contacts).Name);
            }
            HashSet<Contacts> contacts = new HashSet<Contacts>();

            if (this._db.SqlServer == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.SqlServer.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.SqlServer.Open();

                using (var command = new SqlCommand("SELECT * FROM Contacts", this._db.SqlServer))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Contacts contact = new Contacts()
                            {
                                Id = Convert.ToInt32(reader["idContacts"]),
                                Nom = reader["nom"].ToString() ?? string.Empty,
                                Prenom = reader["prenom"].ToString() ?? string.Empty,
                                Civilite = reader["civilite"].ToString() ?? string.Empty,
                                Email = reader["email"].ToString() ?? string.Empty,
                                Telephone = reader["telephone"].ToString() ?? string.Empty,
                                Telephone2 = reader["telephone2"].ToString() ?? string.Empty,
                                Telephone3 = reader["telephone3"].ToString() ?? string.Empty,
                                Mobile = reader["mobile"].ToString() ?? string.Empty,
                                Fonction = reader["fonction"].ToString() ?? string.Empty,
                                CtNum = reader["ct_num"].ToString() ?? string.Empty,
                                CtNo = reader["ct_no"].ToString() ?? string.Empty,
                                DateCreation = Convert.ToDateTime(reader["dateCreation"]),
                                DateModification = Convert.ToDateTime(reader["dateModification"]),
                                Note = reader["note"].ToString() ?? string.Empty,
                                Systematique = Convert.ToBoolean(reader["systematique"]),
                                Facturation = Convert.ToBoolean(reader["facturation"]),
                                Relance = Convert.ToBoolean(reader["relance"])
                            };

                            contacts.Add(contact);
                        }
                    }
                }

                return contacts.Cast<T>().ToHashSet();
            }
            catch (Exception ex)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.SqlServer.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(Contacts))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Contacts).Name);
            }

            if (this._db.SqlServer == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.SqlServer.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.SqlServer.Open();

                using (var command = new SqlCommand("SELECT * FROM Contacts WHERE idContacts = @id", this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            Contacts contact = new Contacts()
                            {
                                Nom = reader["nom"].ToString() ?? string.Empty,
                                Prenom = reader["prenom"].ToString() ?? string.Empty,
                                Civilite = reader["civilite"].ToString() ?? string.Empty,
                                Email = reader["email"].ToString() ?? string.Empty,
                                Telephone = reader["telephone"].ToString() ?? string.Empty,
                                Telephone2 = reader["telephone2"].ToString() ?? string.Empty,
                                Telephone3 = reader["telephone3"].ToString() ?? string.Empty,
                                Mobile = reader["mobile"].ToString() ?? string.Empty,
                                Fonction = reader["fonction"].ToString() ?? string.Empty,
                                CtNum = reader["ct_num"].ToString() ?? string.Empty,
                                CtNo = reader["ct_no"].ToString() ?? string.Empty,
                                DateCreation = Convert.ToDateTime(reader["dateCreation"]),
                                DateModification = Convert.ToDateTime(reader["dateModification"]),
                                Note = reader["note"].ToString() ?? string.Empty,
                                Systematique = Convert.ToBoolean(reader["systematique"]),
                                Facturation = Convert.ToBoolean(reader["facturation"]),
                                Relance = Convert.ToBoolean(reader["relance"])
                            };
                            return (T)(object)contact;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.SqlServer.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(Contacts))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Contacts).Name);
            }
            
            var contact = entity as Contacts;
            if (contact == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.SqlServer == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.SqlServer.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.SqlServer.Open();

                string sql = @"INSERT INTO Contacts (nom, prenom, civilite, email, telephone, telephone2, telephone3, mobile, fonction, ct_num, ct_no, dateCreation, dateModification, note, systematique, facturation, relance) 
                              VALUES (@nom, @prenom, @civilite, @email, @telephone, @telephone2, @telephone3, @mobile, @fonction, @ct_num, @ct_no, @dateCreation, @dateModification, @note, @systematique, @facturation, @relance)";
                using (var command = new SqlCommand(sql, this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@nom", contact.Nom ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@prenom", contact.Prenom ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@civilite", contact.Civilite ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@email", contact.Email ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@telephone", contact.Telephone ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@telephone2", contact.Telephone2 ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@telephone3", contact.Telephone3 ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@mobile", contact.Mobile ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@fonction", contact.Fonction ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ct_num", contact.CtNum ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ct_no", contact.CtNo ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@dateCreation", contact.DateCreation);
                    command.Parameters.AddWithValue("@dateModification", contact.DateModification);
                    command.Parameters.AddWithValue("@note", contact.Note ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@systematique", contact.Systematique);
                    command.Parameters.AddWithValue("@facturation", contact.Facturation);
                    command.Parameters.AddWithValue("@relance", contact.Relance);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.SqlServer.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(Contacts))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Contacts).Name);
            }
            
            var contact = entity as Contacts;
            if (contact == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.SqlServer == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.SqlServer.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.SqlServer.Open();

                string sql = @"UPDATE Contacts SET nom=@nom, prenom=@prenom, ct_num=@ct_num, civilite=@civilite, email=@email, telephone=@telephone, telephone2=@telephone2, telephone3=@telephone3, mobile=@mobile, fonction=@fonction, ct_no=@ct_no, dateCreation=@dateCreation, dateModification=@dateModification, note=@note, systematique=@systematique, facturation=@facturation, relance=@relance WHERE idContacts=@id";
                using (var command = new SqlCommand(sql, this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@id", contact.Id);
                    command.Parameters.AddWithValue("@nom", contact.Nom ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@prenom", contact.Prenom ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@civilite", contact.Civilite ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@email", contact.Email ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@telephone", contact.Telephone ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@telephone2", contact.Telephone2 ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@telephone3", contact.Telephone3 ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@mobile", contact.Mobile ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@fonction", contact.Fonction ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ct_num", contact.CtNum ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ct_no", contact.CtNo ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@dateCreation", contact.DateCreation);
                    command.Parameters.AddWithValue("@dateModification", contact.DateModification);
                    command.Parameters.AddWithValue("@note", contact.Note ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@systematique", contact.Systematique);
                    command.Parameters.AddWithValue("@facturation", contact.Facturation);
                    command.Parameters.AddWithValue("@relance", contact.Relance);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.SqlServer.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(Contacts))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Contacts).Name);
            }
            var contact = entity as Contacts;
            try
            {
                this._db.SqlServer.Open();
                using (var command = new SqlCommand("DELETE FROM Contacts WHERE idContacts = @id", this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@id", contact.Id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.SqlServer.Close();
                throw;
            }
            finally
            {
                this._db.SqlServer.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(Contacts))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Contacts).Name);
            }
            try
            {
                this._db.SqlServer.Open();
                using (var command = new SqlCommand("DELETE FROM Contacts WHERE idContacts = @id", this._db.SqlServer))
                {
                    command.Parameters.AddWithValue("@id", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                this._db.SqlServer.Close();
                throw;
            }
            finally
            {
                this._db.SqlServer.Close();
            }
        }
    }
}
