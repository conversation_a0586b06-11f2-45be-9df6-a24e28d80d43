using HeliosETL.Const;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;
using HeliosETL.Exceptions;

namespace HeliosETL.Repositories.v2
{
    public class TypeProjetRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(TypeProjet))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeProjet).Name);
            }
            HashSet<TypeProjet> typeProjets = new HashSet<TypeProjet>();

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM TypeProjet", this._db.MySqlConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            TypeProjet typeProjet = new TypeProjet()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DomainesMetier = GetDomainesMetierForTypeProjet(Convert.ToInt64(reader["oid"]))
                            };

                            typeProjets.Add(typeProjet);
                        }
                    }
                }

                return typeProjets.Cast<T>().ToHashSet();
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(TypeProjet))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeProjet).Name);
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM TypeProjet WHERE oid = @id", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            TypeProjet typeProjet = new TypeProjet()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DomainesMetier = GetDomainesMetierForTypeProjet(Convert.ToInt64(reader["oid"]))
                            };
                            return (T)(object)typeProjet;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(TypeProjet))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeProjet).Name);
            }
            
            var typeProjet = entity as TypeProjet;
            if (typeProjet == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                string sql = @"INSERT INTO TypeProjet (oid, __version, code, libelle, description, obsolete) VALUES (@oid, @__version, @code, @libelle, @description, @obsolete)";
                using (var command = new MySqlCommand(sql, this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", typeProjet.Oid);
                    command.Parameters.AddWithValue("@__version", typeProjet.__version);
                    command.Parameters.AddWithValue("@code", typeProjet.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", typeProjet.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", typeProjet.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", typeProjet.Obsolete);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(TypeProjet))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeProjet).Name);
            }
            
            var typeProjet = entity as TypeProjet;
            if (typeProjet == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                string sql = @"UPDATE TypeProjet SET __version=@__version, code=@code, libelle=@libelle, description=@description, obsolete=@obsolete WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", typeProjet.Oid);
                    command.Parameters.AddWithValue("@__version", typeProjet.__version);
                    command.Parameters.AddWithValue("@code", typeProjet.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", typeProjet.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", typeProjet.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", typeProjet.Obsolete);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(TypeProjet))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeProjet).Name);
            }
            
            var typeProjet = entity as TypeProjet;
            if (typeProjet == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("DELETE FROM TypeProjet WHERE oid = @oid", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", typeProjet.Oid);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(TypeProjet))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeProjet).Name);
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("DELETE FROM TypeProjet WHERE oid = @oid", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        private HashSet<DomaineMetier> GetDomainesMetierForTypeProjet(long oidTypeProjet)
        {
            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException("MySQL connection is not initialized");
            }

            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();
                using (var command = new MySqlCommand("SELECT dm.* FROM DomaineMetier dm INNER JOIN TypeProjet_DomaineMetier tpdm ON dm.oid = tpdm.oidDomaineMetier WHERE tpdm.oidTypeProjet = @oidTypeProjet", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oidTypeProjet", oidTypeProjet);
                    using (var reader = command.ExecuteReader())
                    {
                        HashSet<DomaineMetier> domainesMetier = new HashSet<DomaineMetier>();
                        while (reader.Read())
                        {
                            DomaineMetier domaineMetier = new DomaineMetier()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DateCreation = Convert.ToDateTime(reader["dateCreation"])
                            };
                            domainesMetier.Add(domaineMetier);
                        }
                        return domainesMetier;
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }
    }
}
