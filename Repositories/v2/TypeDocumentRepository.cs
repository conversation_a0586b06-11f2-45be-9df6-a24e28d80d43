using HeliosETL.Const;
using HeliosETL.Exceptions;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class TypeDocumentRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(TypeDocument))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeDocument).Name);
            }
            HashSet<TypeDocument> typeDocuments = new HashSet<TypeDocument>();

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM TypeDocument", this._db.MySqlConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            TypeDocument typeDocument = new TypeDocument()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DomainesMetier = GetDomainesMetierForTypeDocument(Convert.ToInt64(reader["oid"]))
                            };

                            typeDocuments.Add(typeDocument);
                        }
                    }
                }

                return typeDocuments.Cast<T>().ToHashSet();
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(TypeDocument))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeDocument).Name);
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM TypeDocument WHERE oid = @id", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            TypeDocument typeDocument = new TypeDocument()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DomainesMetier = GetDomainesMetierForTypeDocument(Convert.ToInt64(reader["oid"]))
                            };
                            return (T)(object)typeDocument;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(TypeDocument))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeDocument).Name);
            }
            
            var typeDocument = entity as TypeDocument;
            if (typeDocument == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                string sql = @"INSERT INTO TypeDocument (oid, __version, code, libelle, description, obsolete) VALUES (@oid, @__version, @code, @libelle, @description, @obsolete)";
                using (var command = new MySqlCommand(sql, this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", typeDocument.Oid);
                    command.Parameters.AddWithValue("@__version", typeDocument.__version);
                    command.Parameters.AddWithValue("@code", typeDocument.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", typeDocument.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", typeDocument.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", typeDocument.Obsolete);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(TypeDocument))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeDocument).Name);
            }
            
            var typeDocument = entity as TypeDocument;
            if (typeDocument == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;

            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                string sql =
                    @"UPDATE TypeDocument SET __version=@__version, code=@code, libelle=@libelle, description=@description, obsolete=@obsolete WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", typeDocument.Oid);
                    command.Parameters.AddWithValue("@__version", typeDocument.__version);
                    command.Parameters.AddWithValue("@code", typeDocument.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", typeDocument.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", typeDocument.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", typeDocument.Obsolete);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(TypeDocument))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeDocument).Name);
            }
            
            var typeDocument = entity as TypeDocument;
            if (typeDocument == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("DELETE FROM TypeDocument WHERE oid = @oid", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", typeDocument.Oid);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(TypeDocument))
            {
                throw new ArgumentException(Error.InvalidType, typeof(TypeDocument).Name);
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("DELETE FROM TypeDocument WHERE oid = @oid", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        private HashSet<DomaineMetier> GetDomainesMetierForTypeDocument(long oidTypeDocument)
        {
            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("SELECT dm.* FROM DomaineMetier dm INNER JOIN TypeDocument_DomaineMetier tddm ON dm.oid = tddm.oidDomaineMetier WHERE tddm.oidTypeDocument = @oidTypeDocument", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oidTypeDocument", oidTypeDocument);
                    using (var reader = command.ExecuteReader())
                    {
                        HashSet<DomaineMetier> domainesMetier = new HashSet<DomaineMetier>();
                        while (reader.Read())
                        {
                            DomaineMetier domaineMetier = new DomaineMetier()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DateCreation = Convert.ToDateTime(reader["dateCreation"])
                            };
                            domainesMetier.Add(domaineMetier);
                        }
                        return domainesMetier;
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }
    }
}
