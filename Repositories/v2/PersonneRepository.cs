using HeliosETL.Const;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;
using HeliosETL.Exceptions;

namespace HeliosETL.Repositories.v2
{
    public class PersonneRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(Personne))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Personne).Name);
            }
            HashSet<Personne> personnes = new HashSet<Personne>();

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM Personne", this._db.MySqlConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Personne personne = new Personne()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Externe = Convert.ToBoolean(reader["externe"]),
                                Nom = reader["nom"].ToString() ?? string.Empty,
                                Prenom = reader["prenom"].ToString() ?? string.Empty,
                                Fonction = reader["fonction"].ToString() ?? string.Empty,
                                Email = reader["email"].ToString() ?? string.Empty,
                                Telephone = reader["telephone"].ToString() ?? string.Empty,
                                Mobile = reader["mobile"].ToString() ?? string.Empty,
                                TypePersonne = GetTypePersonneForPersonne(Convert.ToInt64(reader["oidTypePersonne"]))
                            };

                            personnes.Add(personne);
                        }
                    }
                }

                return personnes.Cast<T>().ToHashSet();
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(Personne))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Personne).Name);
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM Personne WHERE oid = @oid", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            Personne personne = new Personne()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Externe = Convert.ToBoolean(reader["externe"]),
                                Nom = reader["nom"].ToString() ?? string.Empty,
                                Prenom = reader["prenom"].ToString() ?? string.Empty,
                                Fonction = reader["fonction"].ToString() ?? string.Empty,
                                Email = reader["email"].ToString() ?? string.Empty,
                                Telephone = reader["telephone"].ToString() ?? string.Empty,
                                Mobile = reader["mobile"].ToString() ?? string.Empty,
                                TypePersonne = GetTypePersonneForPersonne(Convert.ToInt64(reader["oidTypePersonne"]))
                            };
                            return (T)(object)personne;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(Personne))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Personne).Name);
            }
            
            var personne = entity as Personne;
            if (personne == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                string sql = @"INSERT INTO Personne (oid, __version, externe, nom, prenom, fonction, email, telephone, mobile, oidTypePersonne) VALUES (@oid, @__version, @externe, @nom, @prenom, @fonction, @email, @telephone, @mobile, @oidTypePersonne)";
                using (var command = new MySqlCommand(sql, this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", personne.Oid);
                    command.Parameters.AddWithValue("@__version", personne.__version);
                    command.Parameters.AddWithValue("@externe", personne.Externe);
                    command.Parameters.AddWithValue("@nom", personne.Nom ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@prenom", personne.Prenom ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@fonction", personne.Fonction ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@email", personne.Email ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@telephone", personne.Telephone ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@mobile", personne.Mobile ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@oidTypePersonne", personne.TypePersonne?.Oid ?? (object)DBNull.Value);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(Personne))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Personne).Name);
            }
            
            var personne = entity as Personne;
            if (personne == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                string sql = @"UPDATE Personne SET __version=@__version, externe=@externe, nom=@nom, prenom=@prenom, fonction=@fonction, email=@email, telephone=@telephone, mobile=@mobile, oidTypePersonne=@oidTypePersonne WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", personne.Oid);
                    command.Parameters.AddWithValue("@__version", personne.__version);
                    command.Parameters.AddWithValue("@externe", personne.Externe);
                    command.Parameters.AddWithValue("@nom", personne.Nom ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@prenom", personne.Prenom ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@fonction", personne.Fonction ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@email", personne.Email ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@telephone", personne.Telephone ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@mobile", personne.Mobile ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@oidTypePersonne", personne.TypePersonne?.Oid ?? (object)DBNull.Value);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(Personne))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Personne).Name);
            }
            
            var personne = entity as Personne;
            if (personne == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("DELETE FROM Personne WHERE oid = @oid", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", personne.Oid);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(Personne))
            {
                throw new ArgumentException(Error.InvalidType, typeof(Personne).Name);
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("DELETE FROM Personne WHERE oid = @oid", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        private TypePersonne GetTypePersonneForPersonne(long oidTypePersonne)
        {
            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException("MySQL connection is not initialized");
            }

            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM TypePersonne WHERE oid = @oid", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", oidTypePersonne);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new TypePersonne()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                Interne = Convert.ToBoolean(reader["interne"])
                                // DomainesMetier could be loaded separately if needed
                            };
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
            return null;
        }
    }
}
