using HeliosETL.Const;
using HeliosETL.Exceptions;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class IssueOrigineRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(IssueOrigine))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueOrigine).Name);
            }
            HashSet<IssueOrigine> issueOrigines = new HashSet<IssueOrigine>();

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM IssueOrigine", this._db.MySqlConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            IssueOrigine issueOrigine = new IssueOrigine()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DomainesMetier = GetDomainesMetierForIssueOrigine(Convert.ToInt64(reader["oid"]))
                            };

                            issueOrigines.Add(issueOrigine);
                        }
                    }
                }

                return issueOrigines.Cast<T>().ToHashSet();
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(IssueOrigine))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueOrigine).Name);
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM IssueOrigine WHERE oid = @id", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            IssueOrigine issueOrigine = new IssueOrigine()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DomainesMetier = GetDomainesMetierForIssueOrigine(Convert.ToInt64(reader["oid"]))
                            };
                            return (T)(object)issueOrigine;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(IssueOrigine))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueOrigine).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var issueOrigine = entity as IssueOrigine;
            if (issueOrigine == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                string sql = @"INSERT INTO IssueOrigine (oid, __version, code, libelle, description, obsolete) VALUES (@oid, @__version, @code, @libelle, @description, @obsolete)";
                using (var command = new MySqlCommand(sql, this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", issueOrigine.Oid);
                    command.Parameters.AddWithValue("@__version", issueOrigine.__version);
                    command.Parameters.AddWithValue("@code", issueOrigine.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", issueOrigine.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", issueOrigine.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", issueOrigine.Obsolete);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(IssueOrigine))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueOrigine).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var issueOrigine = entity as IssueOrigine;
            if (issueOrigine == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                string sql = @"UPDATE IssueOrigine SET __version=@__version, code=@code, libelle=@libelle, description=@description, obsolete=@obsolete WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", issueOrigine.Oid);
                    command.Parameters.AddWithValue("@__version", issueOrigine.__version);
                    command.Parameters.AddWithValue("@code", issueOrigine.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", issueOrigine.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", issueOrigine.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", issueOrigine.Obsolete);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(IssueOrigine))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueOrigine).Name);
            }
            
            var issueOrigine = entity as IssueOrigine;
            if (issueOrigine == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("DELETE FROM IssueOrigine WHERE oid = @oid", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", issueOrigine.Oid);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(IssueOrigine))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssueOrigine).Name);
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("DELETE FROM IssueOrigine WHERE oid = @oid", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        private HashSet<DomaineMetier> GetDomainesMetierForIssueOrigine(long oidIssueOrigine)
        {
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();
                using (var command = new MySqlCommand("SELECT dm.* FROM DomaineMetier dm INNER JOIN IssueOrigine_DomaineMetier iodm ON dm.oid = iodm.oidDomaineMetier WHERE iodm.oidIssueOrigine = @oidIssueOrigine", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oidIssueOrigine", oidIssueOrigine);
                    using (var reader = command.ExecuteReader())
                    {
                        HashSet<DomaineMetier> domainesMetier = new HashSet<DomaineMetier>();
                        while (reader.Read())
                        {
                            DomaineMetier domaineMetier = new DomaineMetier()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DateCreation = Convert.ToDateTime(reader["dateCreation"])
                            };
                            domainesMetier.Add(domaineMetier);
                        }
                        return domainesMetier;
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }
    }
}
