using HeliosETL.Const;
using HeliosETL.Exceptions;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class IssuePrioriteRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(IssuePriorite))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssuePriorite).Name);
            }
            HashSet<IssuePriorite> issuePriorites = new HashSet<IssuePriorite>();

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM IssuePriorite", this._db.MySqlConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            IssuePriorite issuePriorite = new IssuePriorite()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                serialVersionUID = Convert.ToInt64(reader["serialVersionUID"]),
                                Grade = Convert.ToByte(reader["grade"]),
                                DomainesMetier = GetDomaineMetierForIssuePriorite(Convert.ToInt64(reader["oid"]))
                            };

                            issuePriorites.Add(issuePriorite);
                        }
                    }
                }

                return issuePriorites.Cast<T>().ToHashSet();
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(IssuePriorite))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssuePriorite).Name);
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM IssuePriorite WHERE oid = @oid", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            IssuePriorite issuePriorite = new IssuePriorite()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                serialVersionUID = Convert.ToInt64(reader["serialVersionUID"]),
                                Grade = Convert.ToByte(reader["grade"]),
                                DomainesMetier = GetDomaineMetierForIssuePriorite(Convert.ToInt64(reader["oid"]))
                            };
                            return (T)(object)issuePriorite;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(IssuePriorite))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssuePriorite).Name);
            }
            
            var issuePriorite = entity as IssuePriorite;
            if (issuePriorite == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                string sql = @"INSERT INTO IssuePriorite (oid, __version, code, libelle, description, obsolete, serialVersionUID, grade) VALUES (@oid, @__version, @code, @libelle, @description, @obsolete, @serialVersionUID, @grade)";
                using (var command = new MySqlCommand(sql, this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", issuePriorite.Oid);
                    command.Parameters.AddWithValue("@__version", issuePriorite.__version);
                    command.Parameters.AddWithValue("@code", issuePriorite.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", issuePriorite.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", issuePriorite.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", issuePriorite.Obsolete);
                    command.Parameters.AddWithValue("@serialVersionUID", issuePriorite.serialVersionUID);
                    command.Parameters.AddWithValue("@grade", issuePriorite.Grade);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(IssuePriorite))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssuePriorite).Name);
            }
            
            var issuePriorite = entity as IssuePriorite;
            if (issuePriorite == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                string sql = @"UPDATE IssuePriorite SET __version=@__version, code=@code, libelle=@libelle, description=@description, obsolete=@obsolete, serialVersionUID=@serialVersionUID, grade=@grade WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", issuePriorite.Oid);
                    command.Parameters.AddWithValue("@__version", issuePriorite.__version);
                    command.Parameters.AddWithValue("@code", issuePriorite.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", issuePriorite.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", issuePriorite.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", issuePriorite.Obsolete);
                    command.Parameters.AddWithValue("@serialVersionUID", issuePriorite.serialVersionUID);
                    command.Parameters.AddWithValue("@grade", issuePriorite.Grade);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(IssuePriorite))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssuePriorite).Name);
            }
            var issuePriorite = entity as IssuePriorite;

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("DELETE FROM IssuePriorite WHERE oid = @oid", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", issuePriorite.Oid);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(IssuePriorite))
            {
                throw new ArgumentException(Error.InvalidType, typeof(IssuePriorite).Name);
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("DELETE FROM IssuePriorite WHERE oid = @oid", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        private HashSet<DomaineMetier> GetDomaineMetierForIssuePriorite(long oidIssuePriorite)
        {
            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            
            HashSet<DomaineMetier> domainesMetier = new HashSet<DomaineMetier>();
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                // Assuming there's a junction table for the many-to-many relationship
                using (var command = new MySqlCommand(@"
                    SELECT dm.* FROM DomaineMetier dm 
                    INNER JOIN IssuePriorite_DomaineMetier ipdm ON dm.oid = ipdm.oidDomaineMetier 
                    WHERE ipdm.oidIssuePriorite = @oidIssuePriorite", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oidIssuePriorite", oidIssuePriorite);
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            DomaineMetier domaineMetier = new DomaineMetier()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DateCreation = Convert.ToDateTime(reader["dateCreation"])
                            };
                            domainesMetier.Add(domaineMetier);
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
            
            return domainesMetier;
        }
    }
}
