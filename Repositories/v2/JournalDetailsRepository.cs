using HeliosETL.Const;
using HeliosETL.Exceptions;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class JournalDetailsRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(JournalDetails))
            {
                throw new ArgumentException(Error.InvalidType, typeof(JournalDetails).Name);
            }
            HashSet<JournalDetails> journalDetails = new HashSet<JournalDetails>();

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM JournalDetails", this._db.MySqlConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            JournalDetails journalDetail = new JournalDetails()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                ProprieteType = reader["proprieteType"].ToString() ?? string.Empty,
                                Propriete = reader["propriete"].ToString() ?? string.Empty,
                                AncienneValeur = reader["ancienneValeur"].ToString() ?? string.Empty,
                                NouvelleValeur = reader["nouvelleValeur"].ToString() ?? string.Empty,
                                journal = GetJournalForJournalDetails(Convert.ToInt64(reader["oidJournal"]))
                            };

                            journalDetails.Add(journalDetail);
                        }
                    }
                }

                return journalDetails.Cast<T>().ToHashSet();
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(JournalDetails))
            {
                throw new ArgumentException(Error.InvalidType, typeof(JournalDetails).Name);
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM JournalDetails WHERE oid = @id", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            JournalDetails journalDetail = new JournalDetails()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                ProprieteType = reader["proprieteType"].ToString() ?? string.Empty,
                                Propriete = reader["propriete"].ToString() ?? string.Empty,
                                AncienneValeur = reader["ancienneValeur"].ToString() ?? string.Empty,
                                NouvelleValeur = reader["nouvelleValeur"].ToString() ?? string.Empty,
                                journal = GetJournalForJournalDetails(Convert.ToInt64(reader["oidJournal"]))
                            };
                            return (T)(object)journalDetail;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(JournalDetails))
            {
                throw new ArgumentException(Error.InvalidType, typeof(JournalDetails).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var journalDetail = entity as JournalDetails;
            if (journalDetail == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                string sql = @"INSERT INTO JournalDetails (oid, __version, proprieteType, propriete, ancienneValeur, nouvelleValeur, oidJournal) VALUES (@oid, @__version, @proprieteType, @propriete, @ancienneValeur, @nouvelleValeur, @oidJournal)";
                using (var command = new MySqlCommand(sql, this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", journalDetail.Oid);
                    command.Parameters.AddWithValue("@__version", journalDetail.__version);
                    command.Parameters.AddWithValue("@proprieteType", journalDetail.ProprieteType ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@propriete", journalDetail.Propriete ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ancienneValeur", journalDetail.AncienneValeur ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@nouvelleValeur", journalDetail.NouvelleValeur ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@oidJournal", journalDetail.journal?.Oid ?? (object)DBNull.Value);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(JournalDetails))
            {
                throw new ArgumentException(Error.InvalidType, typeof(JournalDetails).Name);
            }
            
            var journalDetail = entity as JournalDetails;
            if (journalDetail == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                string sql = @"UPDATE JournalDetails SET __version=@__version, proprieteType=@proprieteType, propriete=@propriete, ancienneValeur=@ancienneValeur, nouvelleValeur=@nouvelleValeur, oidJournal=@oidJournal WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", journalDetail.Oid);
                    command.Parameters.AddWithValue("@__version", journalDetail.__version);
                    command.Parameters.AddWithValue("@proprieteType", journalDetail.ProprieteType ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@propriete", journalDetail.Propriete ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ancienneValeur", journalDetail.AncienneValeur ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@nouvelleValeur", journalDetail.NouvelleValeur ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@oidJournal", journalDetail.journal?.Oid ?? (object)DBNull.Value);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(JournalDetails))
            {
                throw new ArgumentException(Error.InvalidType, typeof(JournalDetails).Name);
            }
            var journalDetail = entity as JournalDetails;

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("DELETE FROM JournalDetails WHERE oid = @oid", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", journalDetail.Oid);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(JournalDetails))
            {
                throw new ArgumentException(Error.InvalidType, typeof(JournalDetails).Name);
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("DELETE FROM JournalDetails WHERE oid = @oid", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        private Journal GetJournalForJournalDetails(long oidJournal)
        {
            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();
                using (var command = new MySqlCommand("SELECT * FROM Journal WHERE oid = @oidJournal", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oidJournal", oidJournal);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Journal()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Note = reader["note"].ToString() ?? string.Empty,
                                DateCreation = Convert.ToDateTime(reader["dateCreation"]),
                                Prive = Convert.ToBoolean(reader["prive"]),
                                Interne = Convert.ToBoolean(reader["interne"])
                            };
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
            return null;
        }
    }
}
