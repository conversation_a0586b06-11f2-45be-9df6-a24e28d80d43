using HeliosETL.Const;
using HeliosETL.Exceptions;
using HeliosETL.Models.v2;
using MySql.Data.MySqlClient;

namespace HeliosETL.Repositories.v2
{
    public class DomaineMetierRepository : Repository, IRepository
    {
        public HashSet<T> GetAll<T>()
        {
            if (typeof(T) != typeof(DomaineMetier))
            {
                throw new ArgumentException(Error.InvalidType, typeof(DomaineMetier).Name);
            }
            HashSet<DomaineMetier> domainesMetier = new HashSet<DomaineMetier>();
            
            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM DomaineMetier", this._db.MySqlConn))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            DomaineMetier domaineMetier = new DomaineMetier()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DateCreation = Convert.ToDateTime(reader["dateCreation"])
                            };

                            domainesMetier.Add(domaineMetier);
                        }
                    }
                }

                return domainesMetier.Cast<T>().ToHashSet();
            }
            catch (Exception ex)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public T GetById<T>(int id)
        {
            if (typeof(T) != typeof(DomaineMetier))
            {
                throw new ArgumentException(Error.InvalidType, typeof(DomaineMetier).Name);
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("SELECT * FROM DomaineMetier WHERE oid = @id", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@id", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            DomaineMetier domaineMetier = new DomaineMetier()
                            {
                                Oid = Convert.ToInt64(reader["oid"]),
                                __version = Convert.ToInt32(reader["__version"]),
                                Code = reader["code"].ToString() ?? string.Empty,
                                Libelle = reader["libelle"].ToString() ?? string.Empty,
                                Description = reader["description"].ToString() ?? string.Empty,
                                Obsolete = Convert.ToBoolean(reader["obsolete"]),
                                DateCreation = Convert.ToDateTime(reader["dateCreation"])
                            };
                            return (T)(object)domaineMetier;
                        }
                        else
                        {
                            return default(T);
                        }
                    }
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Add<T>(T entity)
        {
            if (typeof(T) != typeof(DomaineMetier))
            {
                throw new ArgumentException(Error.InvalidType, typeof(DomaineMetier).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var domaineMetier = entity as DomaineMetier;
            if (domaineMetier == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                string sql = @"INSERT INTO DomaineMetier (oid, __version, code, libelle, description, obsolete, dateCreation) VALUES (@oid, @__version, @code, @libelle, @description, @obsolete, @dateCreation)";
                using (var command = new MySqlCommand(sql, this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", domaineMetier.Oid);
                    command.Parameters.AddWithValue("@__version", domaineMetier.__version);
                    command.Parameters.AddWithValue("@code", domaineMetier.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", domaineMetier.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", domaineMetier.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", domaineMetier.Obsolete);
                    command.Parameters.AddWithValue("@dateCreation", domaineMetier.DateCreation);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Update<T>(T entity)
        {
            if (typeof(T) != typeof(DomaineMetier))
            {
                throw new ArgumentException(Error.InvalidType, typeof(DomaineMetier).Name);
            }
            if (entity == null)
            {
                throw new ArgumentNullException("entity");
            }
            
            var domaineMetier = entity as DomaineMetier;
            if (domaineMetier == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                string sql = @"UPDATE DomaineMetier SET __version=@__version, code=@code, libelle=@libelle, description=@description, obsolete=@obsolete, dateCreation=@dateCreation WHERE oid=@oid";
                using (var command = new MySqlCommand(sql, this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@oid", domaineMetier.Oid);
                    command.Parameters.AddWithValue("@__version", domaineMetier.__version);
                    command.Parameters.AddWithValue("@code", domaineMetier.Code ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@libelle", domaineMetier.Libelle ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@description", domaineMetier.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@obsolete", domaineMetier.Obsolete);
                    command.Parameters.AddWithValue("@dateCreation", domaineMetier.DateCreation);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool Delete<T>(T entity)
        {
            if (typeof(T) != typeof(DomaineMetier))
            {
                throw new ArgumentException(Error.InvalidType, typeof(DomaineMetier).Name);
            }
            
            var domaineMetier = entity as DomaineMetier;
            if (domaineMetier == null)
            {
                throw new ArgumentNullException("entity");
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("DELETE FROM DomaineMetier WHERE oid = @id", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@id", domaineMetier.Oid);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }

        public bool DeleteById<T>(int id)
        {
            if (typeof(T) != typeof(DomaineMetier))
            {
                throw new ArgumentException(Error.InvalidType, typeof(DomaineMetier).Name);
            }

            if (this._db.MySqlConn == null)
            {
                throw new DatabaseException(Error.DatabaseNotInitialized);
            }
            bool shouldCloseConnection = this._db.MySqlConn.State != System.Data.ConnectionState.Open;
            
            try
            {
                if (shouldCloseConnection) this._db.MySqlConn.Open();

                using (var command = new MySqlCommand("DELETE FROM DomaineMetier WHERE oid = @id", this._db.MySqlConn))
                {
                    command.Parameters.AddWithValue("@id", id);
                    int result = command.ExecuteNonQuery();
                    return result > 0;
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                if (shouldCloseConnection) this._db.MySqlConn.Close();
            }
        }
    }
}